'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { AudioRecorder } from '@/components/AudioRecorder'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function CreateAudioPage() {
  const [step, setStep] = useState<'record' | 'preview' | 'publish'>('record')
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [audioUrl, setAudioUrl] = useState<string>('')
  const [r2PublicUrl, setR2PublicUrl] = useState<string>('')
  const [r2Key, setR2Key] = useState<string>('')
  const [duration, setDuration] = useState<number>(0)
  const [description, setDescription] = useState<string>('')
  const [isPublishing, setIsPublishing] = useState(false)
  const [isUploading, setIsUploading] = useState(false)
  const router = useRouter()

  const handleRecordingComplete = async (blob: Blob, recordedDuration: number) => {
    console.log('Recording completed with duration:', recordedDuration)
    setAudioBlob(blob)
    setDuration(recordedDuration)
    setIsUploading(true)

    try {
      // Upload to R2 immediately so preview uses same system as Timeline
      const roundedDuration = Math.round(recordedDuration * 10) / 10
      const uploadResponse = await fetch('/api/audio/upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'post',
          duration: roundedDuration
        })
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to get upload URL')
      }

      const { uploadUrl, key, publicUrl } = await uploadResponse.json()

      // Upload to R2
      const uploadFileResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: blob,
        headers: {
          'Content-Type': blob.type || 'audio/mp4'
        }
      })

      if (!uploadFileResponse.ok) {
        throw new Error('Failed to upload audio file')
      }

      // Store R2 info and use R2 URL for preview (same as Timeline)
      setR2PublicUrl(publicUrl)
      setR2Key(key)
      setAudioUrl(publicUrl)

    } catch (error) {
      console.error('Upload failed, using blob URL:', error)
      // Fallback to blob URL if upload fails
      const url = URL.createObjectURL(blob)
      setAudioUrl(url)
    } finally {
      setIsUploading(false)
      setStep('preview')
    }
  }

  const handleRecordAgain = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl)
    }
    setAudioBlob(null)
    setAudioUrl('')
    setDuration(0)
    setStep('record')
  }

  const handlePublish = async () => {
    console.log('Publish clicked - Current state:', {
      hasBlob: !!audioBlob,
      duration,
      step,
      blobSize: audioBlob?.size
    })

    if (!audioBlob || duration <= 0) {
      alert('No valid recording found. Please record again.')
      return
    }

    setIsPublishing(true)
    try {
      console.log('Publishing audio with duration:', duration)

      // Use already uploaded R2 URL or upload now as fallback
      let finalPublicUrl = r2PublicUrl
      let finalKey = r2Key

      if (!finalPublicUrl) {
        // Fallback: upload now if not already uploaded
        const roundedDuration = Math.round(duration * 10) / 10
        const uploadResponse = await fetch('/api/audio/upload', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            type: 'post',
            duration: roundedDuration
          })
        })

        if (!uploadResponse.ok) {
          throw new Error('Failed to get upload URL')
        }

        const { uploadUrl, key, publicUrl } = await uploadResponse.json()

        const uploadFileResponse = await fetch(uploadUrl, {
          method: 'PUT',
          body: audioBlob,
          headers: {
            'Content-Type': audioBlob.type || 'audio/mp4'
          }
        })

        if (!uploadFileResponse.ok) {
          throw new Error('Failed to upload audio file')
        }

        finalPublicUrl = publicUrl
        finalKey = key
      }

      // Create audio post in database
      const createPostResponse = await fetch('/api/audio/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          audio_url: finalPublicUrl,
          audio_key: finalKey,
          description: description.trim() || null,
          duration_seconds: duration,
          file_size_bytes: audioBlob.size
        })
      })

      if (!createPostResponse.ok) {
        throw new Error('Failed to create audio post')
      }

      // Success! Redirect to user's profile to see how the post looks
      router.push('/profile')
    } catch (error) {
      console.error('Error publishing audio:', error)
      alert('Failed to publish audio. Please try again.')
    } finally {
      setIsPublishing(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              href="/timeline"
              className="text-gray-600 hover:text-gray-800 font-medium transition-colors"
            >
              ← Back to Timeline
            </Link>
          </div>
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-2">
            Create Audio Post
          </h1>
          <p className="text-gray-600 font-serif">
            Record a 9-second audio message to share with your followers
          </p>
        </div>

        {/* Step indicator */}
        <div className="flex items-center gap-4 mb-8">
          <div className={`flex items-center gap-2 ${step === 'record' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === 'record' ? 'bg-blue-600 text-white' : 'bg-gray-200'
            }`}>
              1
            </div>
            <span className="font-medium">Record</span>
          </div>
          
          <div className="flex-1 h-px bg-gray-200" />
          
          <div className={`flex items-center gap-2 ${step === 'preview' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === 'preview' ? 'bg-blue-600 text-white' : 'bg-gray-200'
            }`}>
              2
            </div>
            <span className="font-medium">Preview</span>
          </div>
        </div>

        {/* Content */}
        {step === 'record' && (
          <AudioRecorder
            onRecordingComplete={handleRecordingComplete}
            onCancel={() => router.push('/timeline')}
            maxDuration={9}
            className="w-full"
          />
        )}

        {isUploading && (
          <div className="text-center py-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
              <span className="text-lg font-medium text-gray-700">Preparing preview...</span>
            </div>
            <p className="text-gray-500">Uploading your audio for the best playback experience</p>
          </div>
        )}

        {step === 'preview' && audioUrl && (
          <div className="space-y-6">
            {/* Audio preview */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-serif text-gray-800 mb-4">Preview Your Audio</h3>
              {/* Simple HTML5 audio for Safari iOS compatibility */}
              <audio
                controls
                src={audioUrl}
                className="w-full"
                preload="auto"
              >
                Your browser does not support the audio element.
              </audio>
              <p className="text-sm text-gray-500 mt-2">Duration: {duration.toFixed(1)}s</p>
            </div>

            {/* Description input */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description (optional, 50 characters max)
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value.slice(0, 50))}
                placeholder="Add a short description..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={2}
                maxLength={50}
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {description.length}/50
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <Button
                onClick={handleRecordAgain}
                variant="outline"
                className="flex-1"
              >
                Record Again
              </Button>
              <Button
                onClick={handlePublish}
                disabled={isPublishing}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isPublishing ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Publishing...
                  </div>
                ) : (
                  'Publish Audio'
                )}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
