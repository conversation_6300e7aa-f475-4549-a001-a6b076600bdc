'use client'

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { AudioRecorder } from '@/components/AudioRecorder'
import { AudioPlayer } from '@/components/AudioPlayer'
import { Button } from '@/components/ui/button'
import Link from 'next/link'

export default function CreateAudioPage() {
  const [step, setStep] = useState<'record' | 'preview' | 'publish'>('record')
  const [audioBlob, setAudioBlob] = useState<Blob | null>(null)
  const [audioUrl, setAudioUrl] = useState<string>('')
  const [duration, setDuration] = useState<number>(0)
  const [description, setDescription] = useState<string>('')
  const [isPublishing, setIsPublishing] = useState(false)
  const router = useRouter()

  const handleRecordingComplete = (blob: Blob, recordedDuration: number) => {
    console.log('Recording completed with duration:', recordedDuration)
    console.log('Blob type:', blob.type)
    console.log('Blob size:', blob.size)

    setAudioBlob(blob)
    setDuration(recordedDuration)

    // Create local URL for preview - use blob URL for all browsers
    const url = URL.createObjectURL(blob)
    console.log('Created blob URL:', url)
    setAudioUrl(url)
    setStep('preview')
  }

  const handleRecordAgain = () => {
    if (audioUrl) {
      URL.revokeObjectURL(audioUrl)
    }
    setAudioBlob(null)
    setAudioUrl('')
    setDuration(0)
    setStep('record')
  }

  const handlePublish = async () => {
    console.log('Publish clicked - Current state:', {
      hasBlob: !!audioBlob,
      duration,
      step,
      blobSize: audioBlob?.size
    })

    if (!audioBlob || duration <= 0) {
      alert('No valid recording found. Please record again.')
      return
    }

    setIsPublishing(true)
    try {
      console.log('Publishing audio with duration:', duration)

      // Step 1: Get upload URL
      const roundedDuration = Math.round(duration * 10) / 10 // Round to 1 decimal place
      const uploadResponse = await fetch('/api/audio/upload', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          type: 'post',
          duration: roundedDuration
        })
      })

      if (!uploadResponse.ok) {
        throw new Error('Failed to get upload URL')
      }

      const { uploadUrl, key, publicUrl } = await uploadResponse.json()

      // Step 2: Upload audio file to R2
      const uploadFileResponse = await fetch(uploadUrl, {
        method: 'PUT',
        body: audioBlob,
        headers: {
          'Content-Type': 'audio/webm'
        }
      })

      if (!uploadFileResponse.ok) {
        throw new Error('Failed to upload audio file')
      }

      // Step 3: Create audio post in database
      const createPostResponse = await fetch('/api/audio/posts', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          audio_url: publicUrl,
          audio_key: key,
          description: description.trim() || null,
          duration_seconds: duration,
          file_size_bytes: audioBlob.size
        })
      })

      if (!createPostResponse.ok) {
        throw new Error('Failed to create audio post')
      }

      // Success! Redirect to user's profile to see how the post looks
      router.push('/profile')
    } catch (error) {
      console.error('Error publishing audio:', error)
      alert('Failed to publish audio. Please try again.')
    } finally {
      setIsPublishing(false)
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-2xl mx-auto px-4 sm:px-6 py-6 sm:py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center gap-4 mb-4">
            <Link
              href="/timeline"
              className="text-gray-600 hover:text-gray-800 font-medium transition-colors"
            >
              ← Back to Timeline
            </Link>
          </div>
          <h1 className="text-2xl sm:text-3xl font-serif text-gray-800 mb-2">
            Create Audio Post
          </h1>
          <p className="text-gray-600 font-serif">
            Record a 9-second audio message to share with your followers
          </p>
        </div>

        {/* Step indicator */}
        <div className="flex items-center gap-4 mb-8">
          <div className={`flex items-center gap-2 ${step === 'record' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === 'record' ? 'bg-blue-600 text-white' : 'bg-gray-200'
            }`}>
              1
            </div>
            <span className="font-medium">Record</span>
          </div>
          
          <div className="flex-1 h-px bg-gray-200" />
          
          <div className={`flex items-center gap-2 ${step === 'preview' ? 'text-blue-600' : 'text-gray-400'}`}>
            <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
              step === 'preview' ? 'bg-blue-600 text-white' : 'bg-gray-200'
            }`}>
              2
            </div>
            <span className="font-medium">Preview</span>
          </div>
        </div>

        {/* Content */}
        {step === 'record' && (
          <AudioRecorder
            onRecordingComplete={handleRecordingComplete}
            onCancel={() => router.push('/timeline')}
            maxDuration={9}
            className="w-full"
          />
        )}

        {step === 'preview' && audioUrl && (
          <div className="space-y-6">
            {/* Audio preview */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
              <h3 className="text-lg font-serif text-gray-800 mb-4">Preview Your Audio</h3>
              <AudioPlayer
                audioUrl={audioUrl}
                duration={duration}
                className="w-full"
              />
            </div>

            {/* Description input */}
            <div className="bg-white rounded-2xl p-6 shadow-sm border border-gray-200">
              <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-2">
                Description (optional, 50 characters max)
              </label>
              <textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value.slice(0, 50))}
                placeholder="Add a short description..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none"
                rows={2}
                maxLength={50}
              />
              <div className="text-right text-xs text-gray-500 mt-1">
                {description.length}/50
              </div>
            </div>

            {/* Actions */}
            <div className="flex gap-3">
              <Button
                onClick={handleRecordAgain}
                variant="outline"
                className="flex-1"
              >
                Record Again
              </Button>
              <Button
                onClick={handlePublish}
                disabled={isPublishing}
                className="flex-1 bg-blue-600 hover:bg-blue-700 text-white"
              >
                {isPublishing ? (
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    Publishing...
                  </div>
                ) : (
                  'Publish Audio'
                )}
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
